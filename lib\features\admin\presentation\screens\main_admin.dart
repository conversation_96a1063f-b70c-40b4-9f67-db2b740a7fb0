import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_admin_scaffold/admin_scaffold.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:onechurch/core/app/utils/size_config.dart';
import 'package:onechurch/features/admin/controllers/admin_controller.dart';
import 'package:onechurch/features/attendance/presentation/screens/attendance_screen.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import 'package:onechurch/features/dashboard/presentation/screens/dashboard_screen.dart';
import 'package:onechurch/features/events/presentation/screens/events_screen.dart';
import 'package:onechurch/features/hymns/presentation/screens/hymns_screen.dart';
import 'package:onechurch/features/inventory/presentation/screens/inventory_dashboard_screen.dart';
import 'package:onechurch/features/inventory/presentation/screens/inventory_items_screen.dart';
import 'package:onechurch/features/inventory/presentation/screens/inventory_records_screen.dart';
import 'package:onechurch/features/inventory/presentation/screens/inventory_categories/inventory_categories_screen.dart';
import 'package:onechurch/features/members/presentation/screens/member_cateories/member_categories_screen.dart';
import 'package:onechurch/features/members/presentation/screens/members_screen/members_screen.dart';
import 'package:onechurch/features/announcements/presentation/screens/announcements_screen.dart';
import 'package:onechurch/features/sermons/presentation/screens/sermon_screens.dart';
import 'package:onechurch/features/sermons/presentation/screens/create_sermon_screen.dart';
import 'package:onechurch/features/group/presentation/view_groups.dart';
import 'package:onechurch/features/group/presentation/screens/create_group_screen.dart';
import 'package:onechurch/features/events/presentation/admin/create_event.dart';
import 'package:onechurch/features/profile/presentation/screens/profile_screen.dart';
import 'package:onechurch/features/finances/presentation/admin/main_finance.dart';
import 'package:onechurch/features/finances/presentation/screens/account_categories_screen.dart';
import 'package:onechurch/features/communications/presentation/screens/communications_screen.dart';
import 'package:onechurch/features/staff/presentation/screens/create_staff_screen.dart';
import 'package:onechurch/features/staff/presentation/screens/staff_screen.dart';
import 'package:onechurch/features/staff_roles/presentation/screens/staff_roles_screen.dart';

class AdminDrawerScreen extends StatefulWidget {
  final StatefulNavigationShell? navKey;

  const AdminDrawerScreen({super.key, this.navKey});

  @override
  State<AdminDrawerScreen> createState() => _AdminDrawerScreenState();
}

class _AdminDrawerScreenState extends State<AdminDrawerScreen> {
  // Default page and route
  late Widget selectedPage;
  String selectedRoute = Routes.DASHBOARD;

  @override
  void initState() {
    super.initState();
    selectedPage = const DashboardScreen();
  }

  List<AdminMenuItem> _buildMenuItems() {
    return [
      AdminMenuItem(
        title: 'Home',
        icon: Icons.dashboard,
        route: Routes.DASHBOARD,
      ),
      AdminMenuItem(
        title: 'Members',
        icon: Icons.manage_accounts,
        route: Routes.MEMBERS,
        children: [
          AdminMenuItem(
            title: 'View Members',
            icon: Icons.list,
            route: Routes.MEMBERS,
          ),
          AdminMenuItem(
            title: 'Add Member',
            icon: Icons.add,
            route: Routes.ADD_MEMBER,
          ),
          AdminMenuItem(
            title: 'Member Categories',
            icon: Icons.category,
            route: Routes.MEMBER_CATEGORIES,
          ),
          AdminMenuItem(
            title: 'Attendance',
            icon: Icons.how_to_reg,
            route: Routes.ATTENDANCE,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Announcements',
        icon: Icons.announcement_outlined,
        route: Routes.ANNOUNCEMENTS,
        children: [
          AdminMenuItem(
            title: 'View Announcements',
            icon: Icons.list,
            route: Routes.ANNOUNCEMENTS,
          ),
          AdminMenuItem(
            title: 'Create Announcement',
            icon: Icons.add,
            route: Routes.CREATE_ANNOUNCEMENT,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Staff',
        icon: Icons.people,
        route: Routes.STAFF,
        children: [
          AdminMenuItem(
            title: 'View staffs',
            icon: Icons.people_outline,
            route: Routes.STAFF,
          ),
          AdminMenuItem(
            title: 'Create staff',
            icon: Icons.person_add,
            route: Routes.CREATE_STAFF,
          ),
          AdminMenuItem(
            title: 'Staff Roles',
            icon: Icons.supervised_user_circle,
            route: Routes.STAFF_ROLES,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Sermons',
        icon: Icons.church,
        route: Routes.SERMONS,
        children: [
          AdminMenuItem(
            title: 'View Sermons',
            icon: Icons.list,
            route: Routes.SERMONS,
          ),
          AdminMenuItem(
            title: 'Create Sermon',
            icon: Icons.add,
            route: Routes.SERMON_CREATE,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Events',
        icon: Icons.event,
        route: Routes.EVENTS,
        children: [
          AdminMenuItem(
            title: 'View Events',
            icon: Icons.list,
            route: Routes.EVENTS,
          ),
          AdminMenuItem(
            title: 'Create Event',
            icon: Icons.add,
            route: Routes.EVENT_CREATE,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Groups',
        icon: IconlyBold.user3,
        route: Routes.GROUPS,
        children: [
          AdminMenuItem(
            title: 'View Groups',
            icon: Icons.group,
            route: Routes.GROUPS,
          ),
          AdminMenuItem(
            title: 'Create Group',
            icon: Icons.group_add,
            route: Routes.CREATE_GROUP,
          ),
        ],
      ),

      AdminMenuItem(
        title: 'Finance',
        icon: CupertinoIcons.money_dollar_circle_fill,
        route: Routes.FINANCE_ADMIN,
        children: [
          AdminMenuItem(
            title: 'Transactions',
            icon: IconlyBold.chart,
            route: Routes.TRANSACTIONS_VIEW,
          ),
          AdminMenuItem(
            title: 'View Accounts',
            icon: Icons.list,
            route: Routes.SUB_ACCOUNTS_VIEW,
          ),
          AdminMenuItem(
            title: 'Create Account',
            icon: Icons.add,
            route: Routes.CREATE_SUB_ACCOUNT,
          ),
          AdminMenuItem(
            title: 'Account Categories',
            icon: Icons.category,
            route: Routes.ACCOUNT_CATEGORIES,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Inventory',
        icon: Icons.inventory_2,
        route: Routes.INVENTORY_ITEMS,
        children: [
          AdminMenuItem(
            title: 'Items',
            icon: Icons.inventory,
            route: Routes.INVENTORY_ITEMS,
          ),
          AdminMenuItem(
            title: 'Records',
            icon: Icons.description,
            route: Routes.INVENTORY_RECORDS,
          ),
          AdminMenuItem(
            title: 'Item Categories',
            icon: Icons.category,
            route: Routes.INVENTORY_CATEGORIES,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Communications',
        icon: Icons.message,
        route: Routes.COMMUNICATIONS,
        children: [
          AdminMenuItem(title: 'Sms', icon: Icons.sms, route: Routes.SMS),
          AdminMenuItem(
            title: 'Sms Requests',
            icon: Icons.sms_outlined,
            route: Routes.SMS_REQUESTS,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Profile',
        icon: Icons.person,
        route: Routes.PROFILE,
        children: [
          AdminMenuItem(
            title: 'View Profile',
            icon: Icons.person_outline,
            route: Routes.PROFILE,
          ),
          AdminMenuItem(
            title: 'Settings',
            icon: Icons.settings,
            route: Routes.SETTINGS,
          ),
        ],
      ),
    ];
  }

  AdminController adminController = Get.put(AdminController());
  AuthController authController = Get.find();
  @override
  Widget build(BuildContext context) {
    SizeConfig().init(context);

    return AdminScaffold(
      // Use the navigation key from the StatefulNavigationShell instead of a separate key
      appBar: AppBar(
        actions: [
          IconButton(
            onPressed: () {
              context.go(Routes.NOTIFICATIONS);
            },
            icon: Icon(IconlyBold.notification),
          ),
        ],
        flexibleSpace: SizedBox.shrink(),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        centerTitle: true,
        title: Obx(() => Text(adminController.pagtitle.value)),
      ),
      sideBar: SideBar(
        backgroundColor: Theme.of(context).secondaryHeaderColor,

        items: _buildMenuItems(),
        selectedRoute: selectedRoute,
        // Don't use a GlobalKey for the sidebar to avoid duplicate key errors
        onSelected: (item) {
          if (item.route != null) {
            context.go('${item.route}');
            setState(() {
              adminController.pagtitle.value = item.title;
              selectedRoute = item.route!;
              // Update the selected page based on the route
              switch (item.route) {
                case Routes.DASHBOARD:
                  selectedPage = const DashboardScreen();
                  break;
                case Routes.HYMNS:
                  selectedPage = const HymnsScreen();
                  break;
                case Routes.MEMBERS:
                  selectedPage = const MemberViewScreen();
                  break;
                case Routes.EVENTS:
                  selectedPage = const EventsScreen();
                  break;
                case Routes.EVENT_CREATE:
                  selectedPage = const CreateEventScreen();
                  break;
                case Routes.MEMBER_CATEGORIES:
                  selectedPage = const MemberCategoriesScreen();
                  break;
                case Routes.ANNOUNCEMENTS:
                  selectedPage = const AnnouncementsScreen();
                  break;
                case Routes.SERMONS:
                  selectedPage = const ViewSermonsGrid();
                  break;
                case Routes.SERMON_CREATE:
                  selectedPage = const CreateSermonScreen();
                  break;
                case Routes.GROUPS:
                  selectedPage = const ViewGroupsGrid();
                  break;
                case Routes.CREATE_GROUP:
                  selectedPage = const CreateGroupScreen();
                  break;
                case Routes.FINANCE_ADMIN:
                  selectedPage = const SubAccountsView();
                  break;
                case Routes.ACCOUNT_CATEGORIES:
                  selectedPage = const AccountCategoriesScreen();
                  break;
                case Routes.INVENTORY:
                  selectedPage = const InventoryDashboardScreen();
                  break;
                case Routes.INVENTORY_ITEMS:
                  selectedPage = const InventoryItemsScreen();
                  break;
                case Routes.INVENTORY_RECORDS:
                  selectedPage = const InventoryRecordsScreen();
                  break;
                case Routes.INVENTORY_CATEGORIES:
                  selectedPage = const InventoryCategoriesScreen();
                  break;
                case Routes.COMMUNICATIONS:
                  selectedPage = const CommunicationsScreen();
                  break;
                case Routes.STAFF:
                  selectedPage = const StaffScreen();
                  break;
                case Routes.CREATE_STAFF:
                  selectedPage = const CreateStaffScreen();
                  break;
                case Routes.STAFF_ROLES:
                  selectedPage = const StaffRolesScreen();
                  break;
                case Routes.ATTENDANCE:
                  selectedPage = const AttendanceScreen();
                  break;
                case Routes.PROFILE:
                  selectedPage = const ProfileScreen();
                  break;
                case Routes.SETTINGS:
                  selectedPage =
                      const ProfileScreen(); // Using ProfileScreen for settings for now
                  break;
                default:
                  selectedPage = const DashboardScreen();
              }
            });
          }
        },
        header: CachedNetworkImage(
          imageUrl: authController.currentOrg.value?.logo ?? '',
          progressIndicatorBuilder:
              (context, url, downloadProgress) =>
                  CircularProgressIndicator(value: downloadProgress.progress),
          errorWidget: (context, url, error) {
            return Image.asset(
              'assets/logo/onechurch-3.png',
              alignment: Alignment.center,
              fit: BoxFit.contain,
              height: 120.h,
              width: 120.h,
            );
          },
        ),
        footer: _buildHeaderFooter('onechurch'),
      ),
      body: widget.navKey == null ? selectedPage : widget.navKey!,
    );
  }

  Widget _buildHeaderFooter(String text) {
    return Container(
      height: 50,
      width: double.infinity,
      color: Theme.of(context).secondaryHeaderColor,
      child: Center(child: Text(text)),
    );
  }
}
