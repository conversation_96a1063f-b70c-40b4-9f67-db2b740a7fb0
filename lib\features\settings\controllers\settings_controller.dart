import 'package:get/get.dart';

class SettingsController extends GetxController {
  final List<String> settingsItems = [
    'Notifications',
    'Privacy',
    'Terms & Conditions',
    'About',
    'Help',
    'Logout',
  ];
  void updateNotificationSettings(dynamic value){}
  void updateLocationSettings(dynamic value){}
  void updateAutoplaySettings(dynamic value){}
  void updateFontSize(dynamic value){}
  void updateLanguage(dynamic value){}
}