import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/themes/app_theme.dart';
import 'package:onechurch/features/settings/controllers/settings_controller.dart'
    show SettingsController;
import 'package:onechurch/features/settings/presentation/widgets/settings_tile.dart'
    show SettingsTile;
import '../widgets/settings_section.dart';
import '../widgets/settings_tile.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _settingsController = Get.find<SettingsController>();
  bool _notificationsEnabled = true;
  String _selectedLanguage = 'English';
  bool _locationEnabled = true;
  bool _autoplayVideos = false;
  String _fontSizeOption = 'Medium';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    // In a real app, you would load these from shared preferences or other storage
    // This is just a placeholder
    setState(() {
      // Default values already set above
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings'), elevation: 0),
      body: ListView(
        children: [
          const SizedBox(height: 10),

          // Appearance Section
          SettingsSection(
            title: 'Appearance',
            children: [
              SettingsTile(
                title: 'Dark Mode',
                subtitle: 'Toggle between light and dark theme',
                leading: Icon(
                  Get.isDarkMode ? Icons.dark_mode : Icons.light_mode,
                  color: Get.isDarkMode ? Colors.white70 : Colors.amber,
                ),
                trailing: Switch(
                  value: Get.isDarkMode,
                  onChanged: (value) {
                    if (value) {
                      Get.changeTheme(AppTheme.darkTheme());
                    } else {
                      Get.changeTheme(AppTheme.lightTheme());
                    }
                  },
                ),
              ),
              SettingsTile(
                title: 'Font Size',
                subtitle: _fontSizeOption,
                leading: const Icon(Icons.format_size),
                onTap: () => _showFontSizeDialog(),
              ),
            ],
          ),

          // Notifications Section
          SettingsSection(
            title: 'Notifications',
            children: [
              SettingsTile(
                title: 'Push Notifications',
                subtitle:
                    'Get notified about new events, sermons, and announcements',
                leading: const Icon(Icons.notifications),
                trailing: Switch(
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                    _settingsController.updateNotificationSettings(value);
                  },
                ),
              ),
              SettingsTile(
                title: 'Event Reminders',
                subtitle: 'Get reminded about events you\'ve registered for',
                leading: const Icon(Icons.event),
                trailing: Switch(
                  value:
                      _notificationsEnabled, // This would be a separate setting in a real app
                  onChanged:
                      _notificationsEnabled
                          ? (value) {
                            setState(() {});
                          }
                          : null,
                ),
              ),
            ],
          ),

          // General Settings
          SettingsSection(
            title: 'General',
            children: [
              SettingsTile(
                title: 'Language',
                subtitle: _selectedLanguage,
                leading: const Icon(Icons.language),
                onTap: () => _showLanguageDialog(),
              ),
              SettingsTile(
                title: 'Location Services',
                subtitle: 'Enable location for nearby events',
                leading: const Icon(Icons.location_on),
                trailing: Switch(
                  value: _locationEnabled,
                  onChanged: (value) {
                    setState(() {
                      _locationEnabled = value;
                    });
                    _settingsController.updateLocationSettings(value);
                  },
                ),
              ),
              SettingsTile(
                title: 'Media Settings',
                subtitle: 'Autoplay videos',
                leading: const Icon(Icons.play_circle_outline),
                trailing: Switch(
                  value: _autoplayVideos,
                  onChanged: (value) {
                    setState(() {
                      _autoplayVideos = value;
                    });
                    _settingsController.updateAutoplaySettings(value);
                  },
                ),
              ),
            ],
          ),

          // Account Settings
          SettingsSection(
            title: 'Account',
            children: [
              SettingsTile(
                title: 'Edit Profile',
                subtitle: 'Update your personal information',
                leading: const Icon(Icons.person),
                onTap: () {
                  // Navigate to profile editing screen
                  Navigator.pushNamed(context, '/profile/edit');
                },
              ),
              SettingsTile(
                title: 'Privacy Settings',
                subtitle: 'Manage your privacy preferences',
                leading: const Icon(Icons.security),
                onTap: () {
                  // Navigate to privacy settings
                  Navigator.pushNamed(context, '/settings/privacy');
                },
              ),
            ],
          ),

          // About Section
          SettingsSection(
            title: 'About',
            children: [
              SettingsTile(
                title: 'About OneChurch',
                subtitle: 'App version, terms of service, and more',
                leading: const Icon(Icons.info_outline),
                onTap: () {
                  // Show about dialog
                  _showAboutDialog();
                },
              ),
              SettingsTile(
                title: 'Help & Support',
                subtitle: 'Get help using the app',
                leading: const Icon(Icons.help_outline),
                onTap: () {
                  // Navigate to help center
                  Navigator.pushNamed(context, '/help');
                },
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Log Out Button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade800,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onPressed: () {
                // Handle log out
                _showLogOutDialog();
              },
              child: const Text('Log Out', style: TextStyle(fontSize: 16)),
            ),
          ),

          const SizedBox(height: 40),
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    final languages = ['English', 'Spanish', 'French', 'German'];

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Select Language'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  languages.map((language) {
                    return ListTile(
                      title: Text(language),
                      leading: Radio<String>(
                        value: language,
                        groupValue: _selectedLanguage,
                        onChanged: (value) {
                          setState(() {
                            _selectedLanguage = value!;
                          });
                          Navigator.pop(context);
                          _settingsController.updateLanguage(value!);
                        },
                      ),
                    );
                  }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  void _showFontSizeDialog() {
    final fontSizes = ['Small', 'Medium', 'Large', 'Extra Large'];

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Select Font Size'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  fontSizes.map((size) {
                    return ListTile(
                      title: Text(size),
                      leading: Radio<String>(
                        value: size,
                        groupValue: _fontSizeOption,
                        onChanged: (value) {
                          setState(() {
                            _fontSizeOption = value!;
                          });
                          Navigator.pop(context);
                          _settingsController.updateFontSize(value!);
                        },
                      ),
                    );
                  }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('About OneChurch'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('OneChurch App v1.0.0'),
              SizedBox(height: 10),
              Text(
                'A complete church community app that helps connect members, manage events, and share sermons.',
              ),
              SizedBox(height: 20),
              Text('© 2023 OneChurch. All rights reserved.'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  void _showLogOutDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Log Out'),
          content: const Text('Are you sure you want to log out?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Handle log out
                Navigator.pop(context);
                // You'd implement actual logout logic here
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Logged out successfully')),
                );
              },
              child: const Text('Log Out', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
