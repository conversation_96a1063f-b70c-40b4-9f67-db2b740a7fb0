// To parse this JSON data, do
//
//     final organisation = organisationFromJson(jsonString);

import 'dart:convert';

Organisation organisationFromJson(String str) => Organisation.fromJson(json.decode(str));

String organisationToJson(Organisation data) => json.encode(data.toJson());

class Organisation {
    String? id;
    DateTime? createdAt;
    DateTime? updatedAt;
    dynamic deletedAt;
    String? name;
    String? description;
    String? username;
    String? registrationType;
    String? email;
    String? phoneNumber;
    String? businessNumber;
    String? kraPin;
    int? kittyId;
    dynamic totalBalance;
    String? status;
    String? verificationStatus;
    String? logo;
    String? slogan;
    String? referralCode;
    dynamic superOrganisation;
    int? superOrganisationId;
    List<OrgAccount>? accounts;
    int? createdById;

    Organisation({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.name,
        this.description,
        this.username,
        this.registrationType,
        this.email,
        this.phoneNumber,
        this.businessNumber,
        this.kraPin,
        this.kittyId,
        this.totalBalance,
        this.status,
        this.verificationStatus,
        this.logo,
        this.slogan,
        this.referralCode,
        this.superOrganisation,
        this.superOrganisationId,
        this.accounts,
        this.createdById,
    });

    Organisation copyWith({
        String? id,
        DateTime? createdAt,
        DateTime? updatedAt,
        dynamic deletedAt,
        String? name,
        String? description,
        String? username,
        String? registrationType,
        String? email,
        String? phoneNumber,
        String? businessNumber,
        String? kraPin,
        int? kittyId,
        dynamic totalBalance,
        String? status,
        String? verificationStatus,
        String? logo,
        String? slogan,
        String? referralCode,
        dynamic superOrganisation,
        int? superOrganisationId,
        List<OrgAccount>? accounts,
        int? createdById,
    }) => 
        Organisation(
            id: id ?? this.id,
            createdAt: createdAt ?? this.createdAt,
            updatedAt: updatedAt ?? this.updatedAt,
            deletedAt: deletedAt ?? this.deletedAt,
            name: name ?? this.name,
            description: description ?? this.description,
            username: username ?? this.username,
            registrationType: registrationType ?? this.registrationType,
            email: email ?? this.email,
            phoneNumber: phoneNumber ?? this.phoneNumber,
            businessNumber: businessNumber ?? this.businessNumber,
            kraPin: kraPin ?? this.kraPin,
            kittyId: kittyId ?? this.kittyId,
            totalBalance: totalBalance ?? this.totalBalance,
            status: status ?? this.status,
            verificationStatus: verificationStatus ?? this.verificationStatus,
            logo: logo ?? this.logo,
            slogan: slogan ?? this.slogan,
            referralCode: referralCode ?? this.referralCode,
            superOrganisation: superOrganisation ?? this.superOrganisation,
            superOrganisationId: superOrganisationId ?? this.superOrganisationId,
            accounts: accounts ?? this.accounts,
            createdById: createdById ?? this.createdById,
        );

    factory Organisation.fromJson(Map<String, dynamic> json) => Organisation(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null ? null : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null ? null : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        name: json["name"],
        description: json["description"],
        username: json["username"],
        registrationType: json["registration_type"],
        email: json["email"],
        phoneNumber: json["phone_number"],
        businessNumber: json["business_number"],
        kraPin: json["kra_pin"],
        kittyId: json["kitty_id"],
        totalBalance: json["total_balance"],
        status: json["status"],
        verificationStatus: json["verification_status"],
        logo: json["logo"],
        slogan: json["slogan"],
        referralCode: json["referral_code"],
        superOrganisation: json["super_organisation"],
        superOrganisationId: json["super_organisation_id"],
        accounts: json["accounts"] == null ? [] : List<OrgAccount>.from(json["accounts"]!.map((x) => OrgAccount.fromJson(x))),
        createdById: json["created_by_id"],
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "name": name,
        "description": description,
        "username": username,
        "registration_type": registrationType,
        "email": email,
        "phone_number": phoneNumber,
        "business_number": businessNumber,
        "kra_pin": kraPin,
        "kitty_id": kittyId,
        "total_balance": totalBalance,
        "status": status,
        "verification_status": verificationStatus,
        "logo": logo,
        "slogan": slogan,
        "referral_code": referralCode,
        "super_organisation": superOrganisation,
        "super_organisation_id": superOrganisationId,
        "accounts": accounts == null ? [] : List<dynamic>.from(accounts!.map((x) => x.toJson())),
        "created_by_id": createdById,
    };
}

class OrgAccount {
    String? id;
    DateTime? createdAt;
    DateTime? updatedAt;
    dynamic deletedAt;
    String? internalAccount;
    String? accountName;
    dynamic balance;
    int? kittyId;
    double? smsUnitsBalance;
    dynamic kittyBalance;
    double? smsRate;
    int? smsThreshhold;
    double? emailRate;
    bool? isPrimary;
    dynamic organisation;
    String? organisationId;
    String? type;
    String? settleAccount;
    String? settleAccountRef;
    int? settleChannel;
    String? settleFrequency;
    dynamic settleDate;

    OrgAccount({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.internalAccount,
        this.accountName,
        this.balance,
        this.kittyId,
        this.smsUnitsBalance,
        this.kittyBalance,
        this.smsRate,
        this.smsThreshhold,
        this.emailRate,
        this.isPrimary,
        this.organisation,
        this.organisationId,
        this.type,
        this.settleAccount,
        this.settleAccountRef,
        this.settleChannel,
        this.settleFrequency,
        this.settleDate,
    });

    OrgAccount copyWith({
        String? id,
        DateTime? createdAt,
        DateTime? updatedAt,
        dynamic deletedAt,
        String? internalAccount,
        String? accountName,
        dynamic balance,
        int? kittyId,
        double? smsUnitsBalance,
        dynamic kittyBalance,
        double? smsRate,
        int? smsThreshhold,
        double? emailRate,
        bool? isPrimary,
        dynamic organisation,
        String? organisationId,
        String? type,
        String? settleAccount,
        String? settleAccountRef,
        int? settleChannel,
        String? settleFrequency,
        dynamic settleDate,
    }) => 
        OrgAccount(
            id: id ?? this.id,
            createdAt: createdAt ?? this.createdAt,
            updatedAt: updatedAt ?? this.updatedAt,
            deletedAt: deletedAt ?? this.deletedAt,
            internalAccount: internalAccount ?? this.internalAccount,
            accountName: accountName ?? this.accountName,
            balance: balance ?? this.balance,
            kittyId: kittyId ?? this.kittyId,
            smsUnitsBalance: smsUnitsBalance ?? this.smsUnitsBalance,
            kittyBalance: kittyBalance ?? this.kittyBalance,
            smsRate: smsRate ?? this.smsRate,
            smsThreshhold: smsThreshhold ?? this.smsThreshhold,
            emailRate: emailRate ?? this.emailRate,
            isPrimary: isPrimary ?? this.isPrimary,
            organisation: organisation ?? this.organisation,
            organisationId: organisationId ?? this.organisationId,
            type: type ?? this.type,
            settleAccount: settleAccount ?? this.settleAccount,
            settleAccountRef: settleAccountRef ?? this.settleAccountRef,
            settleChannel: settleChannel ?? this.settleChannel,
            settleFrequency: settleFrequency ?? this.settleFrequency,
            settleDate: settleDate ?? this.settleDate,
        );

    factory OrgAccount.fromJson(Map<String, dynamic> json) => OrgAccount(
        id: json["id"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"],
        internalAccount: json["internal_account"],
        accountName: json["account_name"],
        balance: json["balance"],
        kittyId: json["kitty_id"],
        smsUnitsBalance: json["sms_units_balance"]?.toDouble(),
        kittyBalance: json["kitty_balance"],
        smsRate: json["sms_rate"]?.toDouble(),
        smsThreshhold: json["sms_threshhold"],
        emailRate: json["email_rate"]?.toDouble(),
        isPrimary: json["is_primary"],
        organisation: json["organisation"],
        organisationId: json["organisation_id"],
        type: json["type"],
        settleAccount: json["settle_account"],
        settleAccountRef: json["settle_account_ref"],
        settleChannel: json["settle_channel"],
        settleFrequency: json["settle_frequency"],
        settleDate: json["settle_date"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt,
        "internal_account": internalAccount,
        "account_name": accountName,
        "balance": balance,
        "kitty_id": kittyId,
        "sms_units_balance": smsUnitsBalance,
        "kitty_balance": kittyBalance,
        "sms_rate": smsRate,
        "sms_threshhold": smsThreshhold,
        "email_rate": emailRate,
        "is_primary": isPrimary,
        "organisation": organisation,
        "organisation_id": organisationId,
        "type": type,
        "settle_account": settleAccount,
        "settle_account_ref": settleAccountRef,
        "settle_channel": settleChannel,
        "settle_frequency": settleFrequency,
        "settle_date": settleDate,
    };
}
